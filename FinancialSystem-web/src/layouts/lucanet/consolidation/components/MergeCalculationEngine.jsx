import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Calculate as CalculateIcon,
  Timeline as TimelineIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  AccountBalance as AccountIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';

// 计算步骤配置
const calculationSteps = [
  {
    id: 'data_validation',
    name: '数据验证',
    description: '验证基础财务数据完整性和准确性',
    estimatedTime: 30,
  },
  {
    id: 'equity_investment',
    name: '长期股权投资抵消',
    description: '计算和处理长期股权投资抵消分录',
    estimatedTime: 60,
  },
  {
    id: 'internal_transactions',
    name: '内部交易抵消',
    description: '处理集团内部销售和采购抵消',
    estimatedTime: 90,
  },
  {
    id: 'internal_debt',
    name: '内部债权债务抵消',
    description: '抵消集团内部应收应付款项',
    estimatedTime: 45,
  },
  {
    id: 'unrealized_profit',
    name: '未实现利润抵消',
    description: '计算和抵消内部交易未实现利润',
    estimatedTime: 75,
  },
  {
    id: 'minority_interest',
    name: '少数股东权益计算',
    description: '计算少数股东权益和损益',
    estimatedTime: 40,
  },
  {
    id: 'final_calculation',
    name: '合并报表生成',
    description: '生成最终合并财务报表',
    estimatedTime: 50,
  },
  {
    id: 'validation_check',
    name: '平衡验证',
    description: '验证合并报表数据平衡性',
    estimatedTime: 20,
  },
];

// 计算参数配置对话框
const CalculationParametersDialog = ({ open, onClose, onStartCalculation }) => {
  const [parameters, setParameters] = useState({
    period: '',
    method: 'full',
    currency: 'CNY',
    companies: [],
    includeAdjustments: true,
    autoOffset: true,
    parallelProcessing: true,
  });

  const handleStart = () => {
    onStartCalculation(parameters);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>合并计算参数配置</DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="合并期间"
              type="date"
              value={parameters.period}
              onChange={e => setParameters({ ...parameters, period: e.target.value })}
              InputLabelProps={{ shrink: true }}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>合并方法</InputLabel>
              <Select
                value={parameters.method}
                onChange={e => setParameters({ ...parameters, method: e.target.value })}
              >
                <MenuItem value="full">全额合并</MenuItem>
                <MenuItem value="proportional">比例合并</MenuItem>
                <MenuItem value="equity">权益法</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>记账本位币</InputLabel>
              <Select
                value={parameters.currency}
                onChange={e => setParameters({ ...parameters, currency: e.target.value })}
              >
                <MenuItem value="CNY">人民币 (CNY)</MenuItem>
                <MenuItem value="USD">美元 (USD)</MenuItem>
                <MenuItem value="EUR">欧元 (EUR)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>合并范围</InputLabel>
              <Select
                multiple
                value={parameters.companies}
                onChange={e => setParameters({ ...parameters, companies: e.target.value })}
                renderValue={selected => selected.join(', ')}
              >
                <MenuItem value="mother">母公司</MenuItem>
                <MenuItem value="subsidiary_a">子公司A</MenuItem>
                <MenuItem value="subsidiary_b">子公司B</MenuItem>
                <MenuItem value="subsidiary_c">子公司C</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
          计算选项
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Button
              fullWidth
              variant={parameters.includeAdjustments ? 'contained' : 'outlined'}
              onClick={() =>
                setParameters({
                  ...parameters,
                  includeAdjustments: !parameters.includeAdjustments,
                })
              }
            >
              包含调整分录
            </Button>
          </Grid>
          <Grid item xs={12} md={4}>
            <Button
              fullWidth
              variant={parameters.autoOffset ? 'contained' : 'outlined'}
              onClick={() =>
                setParameters({
                  ...parameters,
                  autoOffset: !parameters.autoOffset,
                })
              }
            >
              自动抵消处理
            </Button>
          </Grid>
          <Grid item xs={12} md={4}>
            <Button
              fullWidth
              variant={parameters.parallelProcessing ? 'contained' : 'outlined'}
              onClick={() =>
                setParameters({
                  ...parameters,
                  parallelProcessing: !parameters.parallelProcessing,
                })
              }
            >
              并行处理
            </Button>
          </Grid>
        </Grid>

        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            预计计算时间: {parameters.parallelProcessing ? '6-8' : '15-20'} 分钟
          </Typography>
        </Alert>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button
          onClick={handleStart}
          variant="contained"
          startIcon={<PlayIcon />}
          disabled={!parameters.period || parameters.companies.length === 0}
        >
          开始计算
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// 计算结果详情对话框
const CalculationResultDialog = ({ open, onClose, result }) => {
  if (!result) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>计算结果详情 - {result.name}</DialogTitle>
      <DialogContent>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  计算统计
                </Typography>
                <Typography variant="body2">
                  总资产: {result.totalAssets?.toLocaleString()}
                </Typography>
                <Typography variant="body2">
                  总负债: {result.totalLiabilities?.toLocaleString()}
                </Typography>
                <Typography variant="body2">
                  所有者权益: {result.totalEquity?.toLocaleString()}
                </Typography>
                <Typography variant="body2">
                  少数股东权益: {result.minorityInterest?.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <SpeedIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  性能指标
                </Typography>
                <Typography variant="body2">计算时间: {result.calculationTime}</Typography>
                <Typography variant="body2">处理数据量: {result.dataVolume}</Typography>
                <Typography variant="body2">内存使用: {result.memoryUsage}</Typography>
                <Typography variant="body2">CPU使用率: {result.cpuUsage}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  验证结果
                </Typography>
                <Typography variant="body2">
                  平衡检查: {result.balanceCheck ? '✓ 通过' : '✗ 失败'}
                </Typography>
                <Typography variant="body2">
                  数据完整性: {result.dataIntegrity ? '✓ 通过' : '✗ 失败'}
                </Typography>
                <Typography variant="body2">
                  抵消验证: {result.offsetValidation ? '✓ 通过' : '✗ 失败'}
                </Typography>
                <Typography variant="body2">异常数据: {result.anomalies} 项</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
          抵消分录汇总
        </Typography>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>抵消类型</TableCell>
                <TableCell align="right">分录数量</TableCell>
                <TableCell align="right">借方合计</TableCell>
                <TableCell align="right">贷方合计</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {(result.offsetSummary || []).map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item.type}</TableCell>
                  <TableCell align="right">{item.count}</TableCell>
                  <TableCell align="right">{item.debitTotal.toLocaleString()}</TableCell>
                  <TableCell align="right">{item.creditTotal.toLocaleString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>关闭</Button>
        <Button startIcon={<DownloadIcon />} variant="outlined">
          导出报告
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// 计算进度监控组件
const CalculationProgress = ({ calculation, onPause, onStop }) => {
  if (!calculation || calculation.status !== 'running') {
    return null;
  }

  const currentStep = calculationSteps.find(step => step.id === calculation.currentStep);
  const currentStepIndex = calculationSteps.findIndex(step => step.id === calculation.currentStep);
  const overallProgress =
    ((currentStepIndex + (calculation.stepProgress || 0) / 100) / calculationSteps.length) * 100;

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          <TimelineIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          计算进度监控
        </Typography>
        <Box>
          <IconButton onClick={() => onPause(calculation.id)} color="warning">
            <PauseIcon />
          </IconButton>
          <IconButton onClick={() => onStop(calculation.id)} color="error">
            <StopIcon />
          </IconButton>
        </Box>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="textSecondary" gutterBottom>
          整体进度: {Math.round(overallProgress)}%
        </Typography>
        <LinearProgress variant="determinate" value={overallProgress} sx={{ mb: 1 }} />
        <Typography variant="caption" color="textSecondary">
          预计剩余时间: {calculation.estimatedTimeRemaining || 'N/A'}
        </Typography>
      </Box>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>详细进度</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Stepper activeStep={currentStepIndex} orientation="vertical">
            {calculationSteps.map((step, index) => (
              <Step key={step.id}>
                <StepLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>{step.name}</Typography>
                    {calculation.currentStep === step.id && <CircularProgress size={16} />}
                  </Box>
                </StepLabel>
                <StepContent>
                  <Typography variant="body2" color="textSecondary">
                    {step.description}
                  </Typography>
                  {calculation.currentStep === step.id && (
                    <Box sx={{ mt: 1 }}>
                      <LinearProgress variant="determinate" value={calculation.stepProgress || 0} />
                      <Typography variant="caption" sx={{ mt: 0.5, display: 'block' }}>
                        {calculation.stepProgress || 0}% -{' '}
                        {calculation.currentOperation || '处理中...'}
                      </Typography>
                    </Box>
                  )}
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

// 主组件
const MergeCalculationEngine = () => {
  const [tabValue, setTabValue] = useState(0);
  const [calculations, setCalculations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [parametersDialogOpen, setParametersDialogOpen] = useState(false);
  const [resultDialogOpen, setResultDialogOpen] = useState(false);
  const [selectedResult, setSelectedResult] = useState(null);

  // 模拟数据
  useEffect(() => {
    setCalculations([
      {
        id: 1,
        name: '2025年6月合并计算',
        status: 'completed',
        startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        endTime: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString(),
        duration: '7分32秒',
        method: 'full',
        companies: ['母公司', '子公司A', '子公司B'],
        totalAssets: 1500000000,
        totalLiabilities: 800000000,
        totalEquity: 700000000,
        minorityInterest: 50000000,
        balanceCheck: true,
        dataIntegrity: true,
        offsetValidation: true,
        anomalies: 0,
      },
      {
        id: 2,
        name: '2025年5月合并计算',
        status: 'completed',
        startTime: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
        endTime: new Date(Date.now() - 47 * 60 * 60 * 1000).toISOString(),
        duration: '8分15秒',
        method: 'full',
        companies: ['母公司', '子公司A', '子公司B'],
        totalAssets: 1480000000,
        totalLiabilities: 790000000,
        totalEquity: 690000000,
        minorityInterest: 48000000,
        balanceCheck: true,
        dataIntegrity: true,
        offsetValidation: true,
        anomalies: 2,
      },
    ]);
  }, []);

  const handleStartCalculation = async parameters => {
    const newCalculation = {
      id: Date.now(),
      name: `${parameters.period} 合并计算`,
      status: 'running',
      startTime: new Date().toISOString(),
      method: parameters.method,
      companies: parameters.companies,
      currentStep: 'data_validation',
      stepProgress: 0,
      currentOperation: '正在验证数据完整性...',
      estimatedTimeRemaining: '约7分钟',
    };

    setCalculations(prev => [newCalculation, ...prev]);

    // 模拟计算进度
    simulateCalculationProgress(newCalculation.id);
  };

  const simulateCalculationProgress = calculationId => {
    let stepIndex = 0;
    let stepProgress = 0;

    const updateProgress = () => {
      setTimeout(() => {
        stepProgress += Math.random() * 30 + 10;

        if (stepProgress >= 100) {
          stepIndex++;
          stepProgress = 0;

          if (stepIndex >= calculationSteps.length) {
            // 计算完成
            setCalculations(prev =>
              prev.map(calc =>
                calc.id === calculationId
                  ? {
                    ...calc,
                    status: 'completed',
                    endTime: new Date().toISOString(),
                    duration: '7分42秒',
                    totalAssets: 1520000000,
                    totalLiabilities: 810000000,
                    totalEquity: 710000000,
                    minorityInterest: 52000000,
                    balanceCheck: true,
                    dataIntegrity: true,
                    offsetValidation: true,
                    anomalies: 1,
                  }
                  : calc,
              ),
            );
            return;
          }
        }

        setCalculations(prev =>
          prev.map(calc =>
            calc.id === calculationId
              ? {
                ...calc,
                currentStep: calculationSteps[stepIndex].id,
                stepProgress: Math.min(stepProgress, 100),
                currentOperation: `正在${calculationSteps[stepIndex].description}...`,
                estimatedTimeRemaining: `约${Math.max(1, 8 - stepIndex)}分钟`,
              }
              : calc,
          ),
        );

        updateProgress();
      }, 2000 + Math.random() * 3000);
    };

    updateProgress();
  };

  const handlePauseCalculation = calculationId => {
    setCalculations(prev =>
      prev.map(calc => (calc.id === calculationId ? { ...calc, status: 'paused' } : calc)),
    );
  };

  const handleStopCalculation = calculationId => {
    setCalculations(prev =>
      prev.map(calc =>
        calc.id === calculationId
          ? {
            ...calc,
            status: 'stopped',
            endTime: new Date().toISOString(),
          }
          : calc,
      ),
    );
  };

  const runningCalculation = calculations.find(calc => calc.status === 'running');

  const renderOverviewTab = () => (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          <CalculateIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          合并计算引擎
        </Typography>
        <Button
          variant="contained"
          startIcon={<PlayIcon />}
          onClick={() => setParametersDialogOpen(true)}
          disabled={!!runningCalculation}
        >
          开始新计算
        </Button>
      </Box>

      {/* 运行中的计算监控 */}
      <CalculationProgress
        calculation={runningCalculation}
        onPause={handlePauseCalculation}
        onStop={handleStopCalculation}
      />

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                总计算次数
              </Typography>
              <Typography variant="h4">{calculations.length}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                运行中
              </Typography>
              <Typography variant="h4" color="primary">
                {calculations.filter(c => c.status === 'running').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                已完成
              </Typography>
              <Typography variant="h4" color="success.main">
                {calculations.filter(c => c.status === 'completed').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                平均时长
              </Typography>
              <Typography variant="h4">7.8分钟</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 最近计算历史 */}
      <Typography variant="h6" gutterBottom>
        计算历史
      </Typography>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>计算任务</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>合并方法</TableCell>
              <TableCell>开始时间</TableCell>
              <TableCell>耗时</TableCell>
              <TableCell>验证结果</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {calculations.map(calc => (
              <TableRow key={calc.id}>
                <TableCell>{calc.name}</TableCell>
                <TableCell>
                  <Chip
                    label={
                      calc.status === 'running'
                        ? '运行中'
                        : calc.status === 'completed'
                          ? '已完成'
                          : calc.status === 'paused'
                            ? '已暂停'
                            : '已停止'
                    }
                    color={
                      calc.status === 'running'
                        ? 'primary'
                        : calc.status === 'completed'
                          ? 'success'
                          : calc.status === 'paused'
                            ? 'warning'
                            : 'default'
                    }
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {calc.method === 'full'
                    ? '全额合并'
                    : calc.method === 'proportional'
                      ? '比例合并'
                      : '权益法'}
                </TableCell>
                <TableCell>{new Date(calc.startTime).toLocaleString()}</TableCell>
                <TableCell>{calc.duration || '-'}</TableCell>
                <TableCell>
                  {calc.status === 'completed' && (
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {calc.balanceCheck && <CheckCircleIcon color="success" fontSize="small" />}
                      {calc.anomalies > 0 && <WarningIcon color="warning" fontSize="small" />}
                    </Box>
                  )}
                </TableCell>
                <TableCell>
                  {calc.status === 'completed' && (
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedResult(calc);
                        setResultDialogOpen(true);
                      }}
                    >
                      <ViewIcon />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
        <Tab label="计算引擎" />
        <Tab label="性能监控" />
        <Tab label="配置管理" />
      </Tabs>

      {tabValue === 0 && renderOverviewTab()}
      {tabValue === 1 && (
        <Box sx={{ p: 3 }}>
          <Alert severity="info">性能监控功能正在开发中...</Alert>
        </Box>
      )}
      {tabValue === 2 && (
        <Box sx={{ p: 3 }}>
          <Alert severity="info">配置管理功能正在开发中...</Alert>
        </Box>
      )}

      {/* 计算参数配置对话框 */}
      <CalculationParametersDialog
        open={parametersDialogOpen}
        onClose={() => setParametersDialogOpen(false)}
        onStartCalculation={handleStartCalculation}
      />

      {/* 计算结果详情对话框 */}
      <CalculationResultDialog
        open={resultDialogOpen}
        onClose={() => {
          setResultDialogOpen(false);
          setSelectedResult(null);
        }}
        result={selectedResult}
      />
    </Box>
  );
};

export default MergeCalculationEngine;
