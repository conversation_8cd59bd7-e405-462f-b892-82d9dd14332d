import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Avatar,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon,
  Computer as ComputerIcon,
  Cloud as CloudIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';

// 监控数据模拟
const generateMockData = () => ({
  systemMetrics: {
    cpuUsage: Math.random() * 80 + 10,
    memoryUsage: Math.random() * 70 + 20,
    diskUsage: Math.random() * 60 + 15,
    networkThroughput: Math.random() * 100 + 50,
  },
  activeCalculations: [
    {
      id: 1,
      name: '2025年7月合并计算',
      status: 'running',
      progress: Math.random() * 80 + 10,
      currentStep: 'internal_transactions',
      stepName: '内部交易抵消',
      startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
      estimatedCompletion: new Date(Date.now() + 30 * 60 * 1000),
      dataVolume: '156MB',
      processedRecords: 45000,
      totalRecords: 60000,
    },
  ],
  recentEvents: [
    {
      id: 1,
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      type: 'info',
      message: '开始处理内部交易抵消',
      details: '共发现356笔内部交易需要抵消',
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      type: 'success',
      message: '长期股权投资抵消完成',
      details: '处理了25个投资项目',
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 25 * 60 * 1000),
      type: 'warning',
      message: '发现数据异常',
      details: '子公司A的资产负债表存在不平衡',
    },
  ],
  performanceHistory: Array.from({ length: 24 }, (_, i) => ({
    time: new Date(Date.now() - (23 - i) * 60 * 60 * 1000),
    cpuUsage: Math.random() * 80 + 10,
    memoryUsage: Math.random() * 70 + 20,
    activeJobs: Math.floor(Math.random() * 5),
  })),
});

// 系统性能监控卡片
const SystemMetricsCard = ({ metrics }) => (
  <Card>
    <CardContent>
      <Typography variant="h6" gutterBottom>
        <ComputerIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        系统性能
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={6} md={3}>
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress
              variant="determinate"
              value={metrics.cpuUsage}
              size={60}
              color={
                metrics.cpuUsage > 80 ? 'error' : metrics.cpuUsage > 60 ? 'warning' : 'primary'
              }
            />
            <Typography variant="body2" sx={{ mt: 1 }}>
              CPU: {metrics.cpuUsage.toFixed(1)}%
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={6} md={3}>
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress
              variant="determinate"
              value={metrics.memoryUsage}
              size={60}
              color={
                metrics.memoryUsage > 80
                  ? 'error'
                  : metrics.memoryUsage > 60
                    ? 'warning'
                    : 'success'
              }
            />
            <Typography variant="body2" sx={{ mt: 1 }}>
              内存: {metrics.memoryUsage.toFixed(1)}%
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={6} md={3}>
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress
              variant="determinate"
              value={metrics.diskUsage}
              size={60}
              color={metrics.diskUsage > 80 ? 'error' : metrics.diskUsage > 60 ? 'warning' : 'info'}
            />
            <Typography variant="body2" sx={{ mt: 1 }}>
              磁盘: {metrics.diskUsage.toFixed(1)}%
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={6} md={3}>
          <Box sx={{ textAlign: 'center' }}>
            <TrendingUpIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            <Typography variant="body2" sx={{ mt: 1 }}>
              网络: {metrics.networkThroughput.toFixed(0)} MB/s
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </CardContent>
  </Card>
);

// 活跃计算任务监控
const ActiveCalculationsCard = ({ calculations }) => (
  <Card>
    <CardContent>
      <Typography variant="h6" gutterBottom>
        <ScheduleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        活跃计算任务
      </Typography>
      {calculations.length === 0 ? (
        <Alert severity="info">当前没有运行中的计算任务</Alert>
      ) : (
        calculations.map(calc => (
          <Box
            key={calc.id}
            sx={{ mb: 3, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}
          >
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}
            >
              <Typography variant="subtitle1">{calc.name}</Typography>
              <Chip
                label={calc.status === 'running' ? '运行中' : '暂停'}
                color={calc.status === 'running' ? 'primary' : 'warning'}
                size="small"
              />
            </Box>

            <Typography variant="body2" color="textSecondary" gutterBottom>
              当前步骤: {calc.stepName}
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="caption">整体进度: {calc.progress.toFixed(1)}%</Typography>
                <Typography variant="caption">
                  {calc.processedRecords.toLocaleString()} / {calc.totalRecords.toLocaleString()}{' '}
                  条记录
                </Typography>
              </Box>
              <LinearProgress variant="determinate" value={calc.progress} />
            </Box>

            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="caption" display="block">
                  开始时间: {calc.startTime.toLocaleTimeString()}
                </Typography>
                <Typography variant="caption" display="block">
                  预计完成: {calc.estimatedCompletion.toLocaleTimeString()}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="caption" display="block">
                  数据量: {calc.dataVolume}
                </Typography>
                <Typography variant="caption" display="block">
                  处理速度:{' '}
                  {(calc.processedRecords / ((Date.now() - calc.startTime) / 1000 / 60)).toFixed(0)}{' '}
                  条/分钟
                </Typography>
              </Grid>
            </Grid>
          </Box>
        ))
      )}
    </CardContent>
  </Card>
);

// 事件日志组件
const EventLogCard = ({ events }) => (
  <Card>
    <CardContent>
      <Typography variant="h6" gutterBottom>
        <TimelineIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        事件日志
      </Typography>
      <List>
        {events.map(event => (
          <ListItem key={event.id} alignItems="flex-start">
            <ListItemIcon>
              {event.type === 'success' && <CheckCircleIcon color="success" />}
              {event.type === 'warning' && <WarningIcon color="warning" />}
              {event.type === 'error' && <ErrorIcon color="error" />}
              {event.type === 'info' && <InfoIcon color="info" />}
            </ListItemIcon>
            <ListItemText
              primary={
                <Box
                  sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Typography variant="subtitle2">{event.message}</Typography>
                  <Typography variant="caption" color="textSecondary">
                    {event.timestamp.toLocaleTimeString()}
                  </Typography>
                </Box>
              }
              secondary={event.details}
            />
          </ListItem>
        ))}
      </List>
    </CardContent>
  </Card>
);

// 性能历史图表（简化版）
const PerformanceHistoryCard = ({ history }) => (
  <Card>
    <CardContent>
      <Typography variant="h6" gutterBottom>
        <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        性能历史趋势
      </Typography>
      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>时间</TableCell>
              <TableCell align="right">CPU使用率</TableCell>
              <TableCell align="right">内存使用率</TableCell>
              <TableCell align="right">活跃任务</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {history.slice(-10).map((record, index) => (
              <TableRow key={index}>
                <TableCell>{record.time.toLocaleTimeString()}</TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                    <LinearProgress
                      variant="determinate"
                      value={record.cpuUsage}
                      sx={{ width: 50, mr: 1 }}
                      color={record.cpuUsage > 80 ? 'error' : 'primary'}
                    />
                    {record.cpuUsage.toFixed(0)}%
                  </Box>
                </TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                    <LinearProgress
                      variant="determinate"
                      value={record.memoryUsage}
                      sx={{ width: 50, mr: 1 }}
                      color={record.memoryUsage > 80 ? 'error' : 'success'}
                    />
                    {record.memoryUsage.toFixed(0)}%
                  </Box>
                </TableCell>
                <TableCell align="right">{record.activeJobs}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </CardContent>
  </Card>
);

// 监控设置对话框
const MonitorSettingsDialog = ({ open, onClose }) => {
  const [settings, setSettings] = useState({
    refreshInterval: 5,
    alertThreshold: 80,
    logLevel: 'info',
    enableNotifications: true,
  });

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>监控设置</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>刷新间隔</InputLabel>
              <Select
                value={settings.refreshInterval}
                onChange={e => setSettings({ ...settings, refreshInterval: e.target.value })}
              >
                <MenuItem value={1}>1秒</MenuItem>
                <MenuItem value={5}>5秒</MenuItem>
                <MenuItem value={10}>10秒</MenuItem>
                <MenuItem value={30}>30秒</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>告警阈值 (%)</InputLabel>
              <Select
                value={settings.alertThreshold}
                onChange={e => setSettings({ ...settings, alertThreshold: e.target.value })}
              >
                <MenuItem value={70}>70%</MenuItem>
                <MenuItem value={80}>80%</MenuItem>
                <MenuItem value={90}>90%</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>日志级别</InputLabel>
              <Select
                value={settings.logLevel}
                onChange={e => setSettings({ ...settings, logLevel: e.target.value })}
              >
                <MenuItem value="debug">调试</MenuItem>
                <MenuItem value="info">信息</MenuItem>
                <MenuItem value="warning">警告</MenuItem>
                <MenuItem value="error">错误</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button variant="contained">保存</Button>
      </DialogActions>
    </Dialog>
  );
};

// 主组件
const MergeProgressMonitor = () => {
  const [monitorData, setMonitorData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);

  // 加载监控数据
  const loadMonitorData = () => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      setMonitorData(generateMockData());
      setLoading(false);
    }, 500);
  };

  // 自动刷新
  useEffect(() => {
    loadMonitorData();

    if (autoRefresh) {
      const interval = setInterval(loadMonitorData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  if (loading || !monitorData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          <SpeedIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          合并进度监控
        </Typography>
        <Box>
          <Button
            variant={autoRefresh ? 'contained' : 'outlined'}
            onClick={() => setAutoRefresh(!autoRefresh)}
            sx={{ mr: 1 }}
          >
            {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
          </Button>
          <IconButton onClick={loadMonitorData}>
            <RefreshIcon />
          </IconButton>
          <IconButton onClick={() => setSettingsOpen(true)}>
            <ExpandMoreIcon />
          </IconButton>
        </Box>
      </Box>

      {/* 概览统计 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                活跃任务
              </Typography>
              <Typography variant="h4" color="primary">
                {monitorData.activeCalculations.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                CPU使用率
              </Typography>
              <Typography
                variant="h4"
                color={monitorData.systemMetrics.cpuUsage > 80 ? 'error' : 'success'}
              >
                {monitorData.systemMetrics.cpuUsage.toFixed(0)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                内存使用率
              </Typography>
              <Typography
                variant="h4"
                color={monitorData.systemMetrics.memoryUsage > 80 ? 'error' : 'success'}
              >
                {monitorData.systemMetrics.memoryUsage.toFixed(0)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                处理记录数
              </Typography>
              <Typography variant="h4">
                {monitorData.activeCalculations
                  .reduce((sum, calc) => sum + calc.processedRecords, 0)
                  .toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 主要监控内容 */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={6}>
          <SystemMetricsCard metrics={monitorData.systemMetrics} />
        </Grid>
        <Grid item xs={12} lg={6}>
          <ActiveCalculationsCard calculations={monitorData.activeCalculations} />
        </Grid>
        <Grid item xs={12} lg={6}>
          <EventLogCard events={monitorData.recentEvents} />
        </Grid>
        <Grid item xs={12} lg={6}>
          <PerformanceHistoryCard history={monitorData.performanceHistory} />
        </Grid>
      </Grid>

      {/* 监控设置对话框 */}
      <MonitorSettingsDialog open={settingsOpen} onClose={() => setSettingsOpen(false)} />
    </Box>
  );
};

export default MergeProgressMonitor;
