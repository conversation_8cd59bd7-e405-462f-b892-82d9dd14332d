import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Card,
  CardContent,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Switch,
  Collapse,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Tooltip,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Rule as RuleIcon,
  AccountBalance as AccountIcon,
  Calculate as CalculateIcon,
  AutoMode as AutoIcon,
  Build as ManualIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

// 抵消分录类型配置
const offsetTypes = {
  investment: { label: '投资抵消', icon: <AccountIcon />, color: 'primary' },
  intercompany: { label: '内部交易', icon: <CalculateIcon />, color: 'secondary' },
  debt: { label: '内部债权债务', icon: <RuleIcon />, color: 'info' },
  dividend: { label: '内部股利', icon: <CheckCircleIcon />, color: 'success' },
  profit: { label: '未实现利润', icon: <WarningIcon />, color: 'warning' },
};

// 审核状态配置
const approvalStatus = {
  draft: { label: '草稿', color: 'default' },
  pending: { label: '待审核', color: 'warning' },
  approved: { label: '已审核', color: 'success' },
  rejected: { label: '已驳回', color: 'error' },
};

// 抵消规则配置对话框
const OffsetRuleDialog = ({ open, onClose, rule, onSave }) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'investment',
    description: '',
    autoExecute: true,
    conditions: [],
    entries: [],
    isActive: true,
  });

  useEffect(() => {
    if (rule) {
      setFormData(rule);
    } else {
      setFormData({
        name: '',
        type: 'investment',
        description: '',
        autoExecute: true,
        conditions: [],
        entries: [],
        isActive: true,
      });
    }
  }, [rule, open]);

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const addCondition = () => {
    setFormData({
      ...formData,
      conditions: [
        ...formData.conditions,
        {
          field: '',
          operator: 'equals',
          value: '',
          logicalOperator: 'and',
        },
      ],
    });
  };

  const addEntry = () => {
    setFormData({
      ...formData,
      entries: [
        ...formData.entries,
        {
          account: '',
          debit: 0,
          credit: 0,
          description: '',
        },
      ],
    });
  };

  const updateCondition = (index, field, value) => {
    const newConditions = [...formData.conditions];
    newConditions[index] = { ...newConditions[index], [field]: value };
    setFormData({ ...formData, conditions: newConditions });
  };

  const updateEntry = (index, field, value) => {
    const newEntries = [...formData.entries];
    newEntries[index] = { ...newEntries[index], [field]: value };
    setFormData({ ...formData, entries: newEntries });
  };

  const removeCondition = index => {
    const newConditions = formData.conditions.filter((_, i) => i !== index);
    setFormData({ ...formData, conditions: newConditions });
  };

  const removeEntry = index => {
    const newEntries = formData.entries.filter((_, i) => i !== index);
    setFormData({ ...formData, entries: newEntries });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>{rule ? '编辑抵消规则' : '创建抵消规则'}</DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* 基本信息 */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="规则名称"
              value={formData.name}
              onChange={e => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel>抵消类型</InputLabel>
              <Select
                value={formData.type}
                onChange={e => setFormData({ ...formData, type: e.target.value })}
              >
                {Object.entries(offsetTypes).map(([key, config]) => (
                  <MenuItem key={key} value={key}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {config.icon}
                      <Typography sx={{ ml: 1 }}>{config.label}</Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="规则描述"
              value={formData.description}
              onChange={e => setFormData({ ...formData, description: e.target.value })}
              margin="normal"
              multiline
              rows={2}
            />
          </Grid>

          {/* 执行设置 */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="subtitle1">执行设置:</Typography>
              <Switch
                checked={formData.autoExecute}
                onChange={e => setFormData({ ...formData, autoExecute: e.target.checked })}
              />
              <Typography variant="body2">
                {formData.autoExecute ? '自动执行' : '手动执行'}
              </Typography>
              <Switch
                checked={formData.isActive}
                onChange={e => setFormData({ ...formData, isActive: e.target.checked })}
              />
              <Typography variant="body2">{formData.isActive ? '启用' : '禁用'}</Typography>
            </Box>
          </Grid>
        </Grid>

        {/* 触发条件 */}
        <Accordion sx={{ mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">触发条件</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mb: 2 }}>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={addCondition}
                size="small"
              >
                添加条件
              </Button>
            </Box>
            {formData.conditions.map((condition, index) => (
              <Card key={index} sx={{ mb: 2, p: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="字段"
                      value={condition.field}
                      onChange={e => updateCondition(index, 'field', e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <FormControl fullWidth size="small">
                      <InputLabel>操作符</InputLabel>
                      <Select
                        value={condition.operator}
                        onChange={e => updateCondition(index, 'operator', e.target.value)}
                      >
                        <MenuItem value="equals">等于</MenuItem>
                        <MenuItem value="not_equals">不等于</MenuItem>
                        <MenuItem value="greater_than">大于</MenuItem>
                        <MenuItem value="less_than">小于</MenuItem>
                        <MenuItem value="contains">包含</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="值"
                      value={condition.value}
                      onChange={e => updateCondition(index, 'value', e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <FormControl fullWidth size="small">
                      <InputLabel>逻辑</InputLabel>
                      <Select
                        value={condition.logicalOperator}
                        onChange={e => updateCondition(index, 'logicalOperator', e.target.value)}
                      >
                        <MenuItem value="and">AND</MenuItem>
                        <MenuItem value="or">OR</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={2}>
                    <IconButton color="error" onClick={() => removeCondition(index)} size="small">
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </Card>
            ))}
          </AccordionDetails>
        </Accordion>

        {/* 分录模板 */}
        <Accordion sx={{ mt: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">分录模板</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mb: 2 }}>
              <Button variant="outlined" startIcon={<AddIcon />} onClick={addEntry} size="small">
                添加分录
              </Button>
            </Box>
            {formData.entries.map((entry, index) => (
              <Card key={index} sx={{ mb: 2, p: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="科目"
                      value={entry.account}
                      onChange={e => updateEntry(index, 'account', e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      label="借方"
                      type="number"
                      value={entry.debit}
                      onChange={e => updateEntry(index, 'debit', parseFloat(e.target.value) || 0)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      label="贷方"
                      type="number"
                      value={entry.credit}
                      onChange={e => updateEntry(index, 'credit', parseFloat(e.target.value) || 0)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="摘要"
                      value={entry.description}
                      onChange={e => updateEntry(index, 'description', e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={1}>
                    <IconButton color="error" onClick={() => removeEntry(index)} size="small">
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </Card>
            ))}
          </AccordionDetails>
        </Accordion>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button onClick={handleSave} variant="contained">
          保存
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// 抵消分录详情对话框
const OffsetEntryDetailDialog = ({ open, onClose, entry }) => {
  if (!entry) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>抵消分录详情</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" gutterBottom>
              基本信息
            </Typography>
            <Typography variant="body2">编号: {entry.id}</Typography>
            <Typography variant="body2">名称: {entry.name}</Typography>
            <Typography variant="body2">类型: {offsetTypes[entry.type]?.label}</Typography>
            <Typography variant="body2">状态: {approvalStatus[entry.status]?.label}</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" gutterBottom>
              时间信息
            </Typography>
            <Typography variant="body2">
              创建时间: {new Date(entry.createdAt).toLocaleString()}
            </Typography>
            <Typography variant="body2">
              更新时间: {new Date(entry.updatedAt).toLocaleString()}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              描述
            </Typography>
            <Typography variant="body2">{entry.description}</Typography>
          </Grid>
        </Grid>

        <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
          分录明细
        </Typography>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>科目</TableCell>
                <TableCell align="right">借方</TableCell>
                <TableCell align="right">贷方</TableCell>
                <TableCell>摘要</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {(entry.entries || []).map((entryDetail, index) => (
                <TableRow key={index}>
                  <TableCell>{entryDetail.account}</TableCell>
                  <TableCell align="right">
                    {entryDetail.debit ? entryDetail.debit.toLocaleString() : '-'}
                  </TableCell>
                  <TableCell align="right">
                    {entryDetail.credit ? entryDetail.credit.toLocaleString() : '-'}
                  </TableCell>
                  <TableCell>{entryDetail.description}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>关闭</Button>
      </DialogActions>
    </Dialog>
  );
};

// 主组件
const OffsetEntryManagement = () => {
  const [tabValue, setTabValue] = useState(0);
  const [offsetRules, setOffsetRules] = useState([]);
  const [offsetEntries, setOffsetEntries] = useState([]);
  const [loading, setLoading] = useState(false);
  const [ruleDialogOpen, setRuleDialogOpen] = useState(false);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState(null);
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // 模拟数据
  useEffect(() => {
    setOffsetRules([
      {
        id: 1,
        name: '长期股权投资抵消',
        type: 'investment',
        description: '母公司长期股权投资与子公司所有者权益抵消',
        autoExecute: true,
        isActive: true,
        lastExecuted: new Date().toISOString(),
        executionCount: 15,
      },
      {
        id: 2,
        name: '内部销售抵消',
        type: 'intercompany',
        description: '集团内部销售收入与销售成本抵消',
        autoExecute: true,
        isActive: true,
        lastExecuted: new Date().toISOString(),
        executionCount: 23,
      },
      {
        id: 3,
        name: '内部债权债务抵消',
        type: 'debt',
        description: '集团内部应收应付款项抵消',
        autoExecute: false,
        isActive: true,
        lastExecuted: null,
        executionCount: 0,
      },
    ]);

    setOffsetEntries([
      {
        id: 1,
        name: '2025年6月投资抵消',
        type: 'investment',
        status: 'approved',
        amount: ********,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        description: '母公司对子公司A的长期股权投资抵消',
        entries: [
          { account: '实收资本', debit: 0, credit: ********, description: '抵消子公司实收资本' },
          { account: '资本公积', debit: 0, credit: ********, description: '抵消子公司资本公积' },
          { account: '盈余公积', debit: 0, credit: 3000000, description: '抵消子公司盈余公积' },
          { account: '未分配利润', debit: 0, credit: 2000000, description: '抵消子公司未分配利润' },
          {
            account: '长期股权投资',
            debit: ********,
            credit: 0,
            description: '抵消母公司长期股权投资',
          },
        ],
      },
      {
        id: 2,
        name: '2025年6月内部销售抵消',
        type: 'intercompany',
        status: 'pending',
        amount: ********,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        description: '子公司A向母公司销售商品',
        entries: [
          { account: '营业收入', debit: ********, credit: 0, description: '抵消内部销售收入' },
          { account: '营业成本', debit: 0, credit: ********, description: '抵消内部销售成本' },
        ],
      },
    ]);
  }, []);

  const handleSaveRule = ruleData => {
    if (selectedRule) {
      setOffsetRules(rules =>
        rules.map(rule => (rule.id === selectedRule.id ? { ...rule, ...ruleData } : rule)),
      );
    } else {
      setOffsetRules(rules => [
        ...rules,
        {
          ...ruleData,
          id: Date.now(),
          lastExecuted: null,
          executionCount: 0,
        },
      ]);
    }
  };

  const handleExecuteRule = ruleId => {
    setOffsetRules(rules =>
      rules.map(rule =>
        rule.id === ruleId
          ? {
            ...rule,
            lastExecuted: new Date().toISOString(),
            executionCount: rule.executionCount + 1,
          }
          : rule,
      ),
    );
  };

  const handleToggleRule = ruleId => {
    setOffsetRules(rules =>
      rules.map(rule => (rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule)),
    );
  };

  const renderRulesTab = () => (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          <RuleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          抵消规则管理
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            setSelectedRule(null);
            setRuleDialogOpen(true);
          }}
        >
          创建规则
        </Button>
      </Box>

      <Grid container spacing={2}>
        {offsetRules.map(rule => (
          <Grid item xs={12} md={6} lg={4} key={rule.id}>
            <Card>
              <CardContent>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    mb: 2,
                  }}
                >
                  <Typography variant="h6" component="div">
                    {rule.name}
                  </Typography>
                  <Chip
                    icon={offsetTypes[rule.type]?.icon}
                    label={offsetTypes[rule.type]?.label}
                    color={offsetTypes[rule.type]?.color}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" color="textSecondary" gutterBottom>
                  {rule.description}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  {rule.autoExecute ? (
                    <Chip icon={<AutoIcon />} label="自动执行" size="small" color="primary" />
                  ) : (
                    <Chip icon={<ManualIcon />} label="手动执行" size="small" />
                  )}
                  <Chip
                    label={rule.isActive ? '启用' : '禁用'}
                    size="small"
                    color={rule.isActive ? 'success' : 'default'}
                  />
                </Box>

                <Typography variant="caption" display="block" gutterBottom>
                  执行次数: {rule.executionCount}
                </Typography>
                <Typography variant="caption" display="block" gutterBottom>
                  最后执行:{' '}
                  {rule.lastExecuted ? new Date(rule.lastExecuted).toLocaleString() : '从未执行'}
                </Typography>

                <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setSelectedRule(rule);
                      setRuleDialogOpen(true);
                    }}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color={rule.isActive ? 'success' : 'default'}
                    onClick={() => handleToggleRule(rule.id)}
                  >
                    {rule.isActive ? <CheckCircleIcon /> : <PauseIcon />}
                  </IconButton>
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => handleExecuteRule(rule.id)}
                    disabled={!rule.isActive}
                  >
                    <PlayIcon />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderEntriesTab = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        <AccountIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        抵消分录列表
      </Typography>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>编号</TableCell>
              <TableCell>名称</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>状态</TableCell>
              <TableCell align="right">金额</TableCell>
              <TableCell>创建时间</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {offsetEntries
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map(entry => (
                <TableRow key={entry.id}>
                  <TableCell>{entry.id}</TableCell>
                  <TableCell>{entry.name}</TableCell>
                  <TableCell>
                    <Chip
                      icon={offsetTypes[entry.type]?.icon}
                      label={offsetTypes[entry.type]?.label}
                      color={offsetTypes[entry.type]?.color}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={approvalStatus[entry.status]?.label}
                      color={approvalStatus[entry.status]?.color}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">{entry.amount.toLocaleString()}</TableCell>
                  <TableCell>{new Date(entry.createdAt).toLocaleString()}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedEntry(entry);
                        setDetailDialogOpen(true);
                      }}
                    >
                      <ViewIcon />
                    </IconButton>
                    <IconButton size="small">
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={offsetEntries.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={(event, newPage) => setPage(newPage)}
        onRowsPerPageChange={event => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(0);
        }}
      />
    </Box>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
        <Tab label="抵消规则" />
        <Tab label="抵消分录" />
        <Tab label="审核工作流" />
      </Tabs>

      {tabValue === 0 && renderRulesTab()}
      {tabValue === 1 && renderEntriesTab()}
      {tabValue === 2 && (
        <Box sx={{ p: 3 }}>
          <Alert severity="info">审核工作流功能正在开发中...</Alert>
        </Box>
      )}

      {/* 抵消规则配置对话框 */}
      <OffsetRuleDialog
        open={ruleDialogOpen}
        onClose={() => {
          setRuleDialogOpen(false);
          setSelectedRule(null);
        }}
        rule={selectedRule}
        onSave={handleSaveRule}
      />

      {/* 抵消分录详情对话框 */}
      <OffsetEntryDetailDialog
        open={detailDialogOpen}
        onClose={() => {
          setDetailDialogOpen(false);
          setSelectedEntry(null);
        }}
        entry={selectedEntry}
      />
    </Box>
  );
};

export default OffsetEntryManagement;
