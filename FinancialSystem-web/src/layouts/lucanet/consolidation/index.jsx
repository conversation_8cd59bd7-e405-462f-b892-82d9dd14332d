import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Tabs,
  Tab,
  Button,
  IconButton,
  Chip,
  Alert,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
  History as HistoryIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Timeline as TimelineIcon,
  Calculate as CalculateIcon,
  Rule as RuleIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';

import { lucaNetMockApi as mockApiService } from '../../../services/lucanet/mockApiService';

// 导入子组件
import OffsetEntryManagement from './components/OffsetEntryManagement';
import MergeCalculationEngine from './components/MergeCalculationEngine';
import MergeRulesConfig from './components/MergeRulesConfig';
import MergeProgressMonitor from './components/MergeProgressMonitor';

// 任务状态映射
const statusConfig = {
  pending: { label: '待开始', color: 'default', icon: <InfoIcon /> },
  running: { label: '运行中', color: 'primary', icon: <PlayIcon /> },
  paused: { label: '已暂停', color: 'warning', icon: <PauseIcon /> },
  completed: { label: '已完成', color: 'success', icon: <CheckCircleIcon /> },
  failed: { label: '失败', color: 'error', icon: <ErrorIcon /> },
};

// 合并任务创建向导组件
const TaskCreationWizard = ({ open, onClose, onTaskCreated }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [taskData, setTaskData] = useState({
    name: '',
    description: '',
    period: '',
    companies: [],
    mergeMethod: 'full',
    autoOffset: true,
    validationRules: [],
  });

  const steps = ['基本信息', '合并范围', '合并规则', '验证设置'];

  const handleNext = () => {
    setActiveStep(prevActiveStep => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep(prevActiveStep => prevActiveStep - 1);
  };

  const handleSubmit = async () => {
    try {
      await mockApiService.createConsolidationTask(taskData);
      onTaskCreated();
      onClose();
      setActiveStep(0);
      setTaskData({
        name: '',
        description: '',
        period: '',
        companies: [],
        mergeMethod: 'full',
        autoOffset: true,
        validationRules: [],
      });
    } catch (error) {
      console.error('创建任务失败:', error);
    }
  };

  const renderStepContent = step => {
    switch (step) {
    case 0:
      return (
        <Box sx={{ pt: 2 }}>
          <TextField
            fullWidth
            label="任务名称"
            value={taskData.name}
            onChange={e => setTaskData({ ...taskData, name: e.target.value })}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="任务描述"
            value={taskData.description}
            onChange={e => setTaskData({ ...taskData, description: e.target.value })}
            margin="normal"
            multiline
            rows={3}
          />
          <TextField
            fullWidth
            label="合并期间"
            value={taskData.period}
            onChange={e => setTaskData({ ...taskData, period: e.target.value })}
            margin="normal"
            type="date"
            InputLabelProps={{ shrink: true }}
            required
          />
        </Box>
      );
    case 1:
      return (
        <Box sx={{ pt: 2 }}>
          <Typography variant="h6" gutterBottom>
              选择合并公司范围
          </Typography>
          <FormControl fullWidth margin="normal">
            <InputLabel>合并公司</InputLabel>
            <Select
              multiple
              value={taskData.companies}
              onChange={e => setTaskData({ ...taskData, companies: e.target.value })}
              renderValue={selected => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map(value => (
                    <Chip key={value} label={value} />
                  ))}
                </Box>
              )}
            >
              {['母公司', '子公司A', '子公司B', '子公司C'].map(company => (
                <MenuItem key={company} value={company}>
                  {company}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      );
    case 2:
      return (
        <Box sx={{ pt: 2 }}>
          <FormControl fullWidth margin="normal">
            <InputLabel>合并方法</InputLabel>
            <Select
              value={taskData.mergeMethod}
              onChange={e => setTaskData({ ...taskData, mergeMethod: e.target.value })}
            >
              <MenuItem value="full">全额合并</MenuItem>
              <MenuItem value="proportional">比例合并</MenuItem>
              <MenuItem value="equity">权益法</MenuItem>
            </Select>
          </FormControl>
          <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
              自动抵消设置
          </Typography>
          <FormControl component="fieldset">
            <Button
              variant={taskData.autoOffset ? 'contained' : 'outlined'}
              onClick={() => setTaskData({ ...taskData, autoOffset: !taskData.autoOffset })}
            >
              {taskData.autoOffset ? '启用自动抵消' : '禁用自动抵消'}
            </Button>
          </FormControl>
        </Box>
      );
    case 3:
      return (
        <Box sx={{ pt: 2 }}>
          <Typography variant="h6" gutterBottom>
              验证规则配置
          </Typography>
          <Alert severity="info" sx={{ mb: 2 }}>
              系统将自动应用标准验证规则，包括资产负债表平衡检查、现金流量表验证等。
          </Alert>
          <List>
            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText primary="资产负债表平衡验证" secondary="确保资产=负债+所有者权益" />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText primary="合并抵消完整性检查" secondary="验证内部交易完全抵消" />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText primary="少数股东权益计算验证" secondary="确保少数股东权益计算正确" />
            </ListItem>
          </List>
        </Box>
      );
    default:
      return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>创建合并任务</DialogTitle>
      <DialogContent>
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              <StepContent>
                {renderStepContent(index)}
                <Box sx={{ mb: 2, mt: 2 }}>
                  <Button
                    variant="contained"
                    onClick={index === steps.length - 1 ? handleSubmit : handleNext}
                    sx={{ mr: 1 }}
                    disabled={
                      (index === 0 && (!taskData.name || !taskData.period)) ||
                      (index === 1 && taskData.companies.length === 0)
                    }
                  >
                    {index === steps.length - 1 ? '创建任务' : '下一步'}
                  </Button>
                  <Button disabled={index === 0} onClick={handleBack}>
                    上一步
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </DialogContent>
    </Dialog>
  );
};

// 合并进度监控组件
const ConsolidationProgressMonitor = ({ task }) => {
  if (!task || task.status !== 'running') {
    return null;
  }

  return (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        <TimelineIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        合并进度监控
      </Typography>

      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="textSecondary" gutterBottom>
          整体进度: {task.progress || 0}%
        </Typography>
        <LinearProgress variant="determinate" value={task.progress || 0} sx={{ mb: 1 }} />
      </Box>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>详细进度</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {(task.steps || []).map((step, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  {step.status === 'completed' && <CheckCircleIcon color="success" />}
                  {step.status === 'running' && <PlayIcon color="primary" />}
                  {step.status === 'pending' && <InfoIcon color="disabled" />}
                  {step.status === 'failed' && <ErrorIcon color="error" />}
                </ListItemIcon>
                <ListItemText primary={step.name} secondary={step.message} />
                {step.status === 'running' && (
                  <LinearProgress variant="indeterminate" sx={{ width: 100, ml: 1 }} />
                )}
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

// 主组件
const ConsolidationManagement = () => {
  const [tabValue, setTabValue] = useState(0);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTask, setSelectedTask] = useState(null);
  const [wizardOpen, setWizardOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 加载合并任务列表
  const loadTasks = async () => {
    try {
      setLoading(true);
      const data = await mockApiService.getConsolidationTasks();
      setTasks(data);
    } catch (error) {
      console.error('加载任务失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新任务状态
  const refreshTasks = async () => {
    setRefreshing(true);
    await loadTasks();
    setRefreshing(false);
  };

  // 启动任务
  const startTask = async taskId => {
    try {
      await mockApiService.startConsolidationTask(taskId);
      await loadTasks();
    } catch (error) {
      console.error('启动任务失败:', error);
    }
  };

  // 暂停任务
  const pauseTask = async taskId => {
    try {
      await mockApiService.pauseConsolidationTask(taskId);
      await loadTasks();
    } catch (error) {
      console.error('暂停任务失败:', error);
    }
  };

  // 停止任务
  const stopTask = async taskId => {
    try {
      await mockApiService.stopConsolidationTask(taskId);
      await loadTasks();
    } catch (error) {
      console.error('停止任务失败:', error);
    }
  };

  useEffect(() => {
    loadTasks();
  }, []);

  const runningTask = tasks.find(task => task.status === 'running');

  const renderTaskCard = task => (
    <Card key={task.id} sx={{ mb: 2 }}>
      <CardContent>
        <Box
          sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}
        >
          <Typography variant="h6" component="div">
            {task.name}
          </Typography>
          <Chip
            icon={statusConfig[task.status]?.icon}
            label={statusConfig[task.status]?.label}
            color={statusConfig[task.status]?.color}
            size="small"
          />
        </Box>

        <Typography variant="body2" color="textSecondary" gutterBottom>
          {task.description}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <Typography variant="caption" display="block">
              合并期间: {task.period}
            </Typography>
            <Typography variant="caption" display="block">
              创建时间: {new Date(task.createdAt).toLocaleString()}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="caption" display="block">
              合并方法:{' '}
              {task.mergeMethod === 'full'
                ? '全额合并'
                : task.mergeMethod === 'proportional'
                  ? '比例合并'
                  : '权益法'}
            </Typography>
            <Typography variant="caption" display="block">
              合并公司: {task.companies?.join(', ')}
            </Typography>
          </Grid>
        </Grid>

        {task.status === 'running' && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" display="block" gutterBottom>
              进度: {task.progress || 0}%
            </Typography>
            <LinearProgress variant="determinate" value={task.progress || 0} />
          </Box>
        )}

        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          {task.status === 'pending' && (
            <Button
              size="small"
              variant="contained"
              startIcon={<PlayIcon />}
              onClick={() => startTask(task.id)}
            >
              启动
            </Button>
          )}
          {task.status === 'running' && (
            <>
              <Button
                size="small"
                variant="outlined"
                startIcon={<PauseIcon />}
                onClick={() => pauseTask(task.id)}
              >
                暂停
              </Button>
              <Button
                size="small"
                variant="outlined"
                color="error"
                startIcon={<StopIcon />}
                onClick={() => stopTask(task.id)}
              >
                停止
              </Button>
            </>
          )}
          {task.status === 'paused' && (
            <Button
              size="small"
              variant="contained"
              startIcon={<PlayIcon />}
              onClick={() => startTask(task.id)}
            >
              继续
            </Button>
          )}
          <Button
            size="small"
            variant="outlined"
            startIcon={<HistoryIcon />}
            onClick={() => setSelectedTask(task)}
          >
            详情
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          <CalculateIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
          合并处理管理
        </Typography>
        <Box>
          <IconButton onClick={refreshTasks} disabled={refreshing}>
            <RefreshIcon />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setWizardOpen(true)}
            sx={{ ml: 1 }}
          >
            创建合并任务
          </Button>
        </Box>
      </Box>

      {/* 运行中的任务监控 */}
      <ConsolidationProgressMonitor task={runningTask} />

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                总任务数
              </Typography>
              <Typography variant="h4">{tasks.length}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                运行中
              </Typography>
              <Typography variant="h4" color="primary">
                {tasks.filter(t => t.status === 'running').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                已完成
              </Typography>
              <Typography variant="h4" color="success.main">
                {tasks.filter(t => t.status === 'completed').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                失败
              </Typography>
              <Typography variant="h4" color="error.main">
                {tasks.filter(t => t.status === 'failed').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 标签页内容 */}
      <Paper sx={{ width: '100%' }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="任务列表" />
          <Tab label="抵消管理" />
          <Tab label="计算引擎" />
          <Tab label="规则配置" />
          <Tab label="进度监控" />
        </Tabs>

        {/* 任务列表 */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            {loading ? (
              <LinearProgress />
            ) : tasks.length === 0 ? (
              <Alert severity="info">暂无合并任务，点击"创建合并任务"开始。</Alert>
            ) : (
              tasks.map(renderTaskCard)
            )}
          </Box>
        )}

        {/* 抵消管理 */}
        {tabValue === 1 && <OffsetEntryManagement />}

        {/* 计算引擎 */}
        {tabValue === 2 && <MergeCalculationEngine />}

        {/* 规则配置 */}
        {tabValue === 3 && <MergeRulesConfig />}

        {/* 进度监控 */}
        {tabValue === 4 && <MergeProgressMonitor />}
      </Paper>

      {/* 任务创建向导 */}
      <TaskCreationWizard
        open={wizardOpen}
        onClose={() => setWizardOpen(false)}
        onTaskCreated={loadTasks}
      />
    </Container>
  );
};

export default ConsolidationManagement;
