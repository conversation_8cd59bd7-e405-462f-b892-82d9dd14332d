/**
 * 批量编辑对话框
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
} from '@mui/material';

const BatchEditDialog = ({ open, onClose, onCompleted, selectedData, chartOfAccounts }) => {
  const [editFields, setEditFields] = useState({
    status: '',
    dataSource: '',
    notes: '',
    adjustmentPercent: '',
  });

  const handleFieldChange = (field, value) => {
    setEditFields(prev => ({ ...prev, [field]: value }));
  };

  const handleApply = () => {
    // 实际应用中会调用API进行批量更新
    console.log('批量编辑应用:', editFields, selectedData);
    onCompleted();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>批量编辑 ({selectedData.length} 条记录)</DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>审核状态</InputLabel>
              <Select
                value={editFields.status}
                onChange={e => handleFieldChange('status', e.target.value)}
                label="审核状态"
              >
                <MenuItem value="">不修改</MenuItem>
                <MenuItem value="DRAFT">草稿</MenuItem>
                <MenuItem value="PENDING">待审核</MenuItem>
                <MenuItem value="APPROVED">已审核</MenuItem>
                <MenuItem value="REJECTED">已拒绝</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>数据来源</InputLabel>
              <Select
                value={editFields.dataSource}
                onChange={e => handleFieldChange('dataSource', e.target.value)}
                label="数据来源"
              >
                <MenuItem value="">不修改</MenuItem>
                <MenuItem value="MANUAL">手动录入</MenuItem>
                <MenuItem value="IMPORT">批量导入</MenuItem>
                <MenuItem value="INTERFACE">接口导入</MenuItem>
                <MenuItem value="SYSTEM">系统生成</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="金额调整百分比"
              value={editFields.adjustmentPercent}
              onChange={e => handleFieldChange('adjustmentPercent', e.target.value)}
              type="number"
              InputProps={{ endAdornment: '%' }}
              helperText="正数为增加，负数为减少"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="批量备注"
              value={editFields.notes}
              onChange={e => handleFieldChange('notes', e.target.value)}
              multiline
              rows={3}
              helperText="将添加到所有选中记录的备注中"
            />
          </Grid>
        </Grid>

        <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
          影响的记录预览
        </Typography>

        <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
          <Table size="small" stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>组织</TableCell>
                <TableCell>科目</TableCell>
                <TableCell>期间</TableCell>
                <TableCell align="right">当前金额</TableCell>
                <TableCell>当前状态</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {selectedData.slice(0, 10).map((row, index) => (
                <TableRow key={index}>
                  <TableCell>{row.organizationName}</TableCell>
                  <TableCell>
                    {row.accountCode} - {row.accountName}
                  </TableCell>
                  <TableCell>{row.period}</TableCell>
                  <TableCell align="right">
                    {parseFloat(row.localAmount || 0).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <Chip label={row.status} size="small" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {selectedData.length > 10 && (
          <Typography variant="body2" sx={{ mt: 1 }}>
            显示前10条记录，共{selectedData.length}条将被修改
          </Typography>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button variant="contained" onClick={handleApply}>
          应用更改
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BatchEditDialog;
