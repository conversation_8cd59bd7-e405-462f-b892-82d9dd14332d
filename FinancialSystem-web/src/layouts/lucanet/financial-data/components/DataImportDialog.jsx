/**
 * 财务数据批量导入对话框
 *
 * 功能特性：
 * - Excel文件上传和解析
 * - 数据预览和验证
 * - 导入进度显示
 * - 错误处理和报告
 * - 模板下载功能
 */

import React, { useState, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Stepper,
  Step,
  StepLabel,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Grid,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Download as DownloadIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Delete as DeleteIcon,
  Visibility as PreviewIcon,
} from '@mui/icons-material';

import { lucaNetMockApi } from '../../../../services/lucanet/mockApiService';

// 导入步骤
const STEPS = ['上传文件', '数据预览', '验证检查', '导入执行'];

// 模拟Excel解析功能
const parseExcelFile = file => {
  return new Promise(resolve => {
    // 模拟Excel解析延迟
    setTimeout(() => {
      // 模拟解析出的数据
      const mockData = [
        {
          row: 1,
          organizationId: '2',
          organizationName: '科技股份有限公司',
          accountCode: '1001',
          accountName: '库存现金',
          period: '2025-06',
          localAmount: 125000.5,
          functionalAmount: 125000.5,
          groupAmount: 125000.5,
          localCurrency: 'CNY',
          functionalCurrency: 'CNY',
          groupCurrency: 'CNY',
          dataSource: 'IMPORT',
          status: 'DRAFT',
        },
        {
          row: 2,
          organizationId: '2',
          organizationName: '科技股份有限公司',
          accountCode: '1002',
          accountName: '银行存款',
          period: '2025-06',
          localAmount: 2850000.0,
          functionalAmount: 2850000.0,
          groupAmount: 2850000.0,
          localCurrency: 'CNY',
          functionalCurrency: 'CNY',
          groupCurrency: 'CNY',
          dataSource: 'IMPORT',
          status: 'DRAFT',
        },
        {
          row: 3,
          organizationId: '3',
          organizationName: '科技(香港)有限公司',
          accountCode: '1001',
          accountName: '库存现金',
          period: '2025-06',
          localAmount: 50000.0,
          functionalAmount: 318500.0, // 假设汇率转换
          groupAmount: 318500.0,
          localCurrency: 'HKD',
          functionalCurrency: 'CNY',
          groupCurrency: 'CNY',
          dataSource: 'IMPORT',
          status: 'DRAFT',
        },
        // 故意添加一些有错误的数据用于演示验证功能
        {
          row: 4,
          organizationId: '999', // 不存在的组织ID
          organizationName: '未知组织',
          accountCode: '9999', // 不存在的科目代码
          accountName: '未知科目',
          period: '2025-06',
          localAmount: 'invalid_amount', // 无效金额
          functionalAmount: 0,
          groupAmount: 0,
          localCurrency: 'CNY',
          functionalCurrency: 'CNY',
          groupCurrency: 'CNY',
          dataSource: 'IMPORT',
          status: 'DRAFT',
          errors: ['组织不存在', '科目代码无效', '金额格式错误'],
        },
      ];

      resolve(mockData);
    }, 1500);
  });
};

// 数据验证功能
const validateImportData = (data, organizations, chartOfAccounts) => {
  const validData = [];
  const invalidData = [];

  data.forEach(item => {
    const errors = [];

    // 验证组织ID
    const orgExists = organizations.some(org => org.id === item.organizationId);
    if (!orgExists) {
      errors.push('组织不存在');
    }

    // 验证科目代码
    const accountExists = chartOfAccounts.some(acc => acc.code === item.accountCode);
    if (!accountExists) {
      errors.push('科目代码无效');
    }

    // 验证金额格式
    if (typeof item.localAmount !== 'number' || isNaN(item.localAmount)) {
      errors.push('金额格式错误');
    }

    // 验证期间格式
    if (!item.period || !/^\d{4}-\d{2}$/.test(item.period)) {
      errors.push('期间格式错误');
    }

    if (errors.length > 0) {
      invalidData.push({ ...item, errors });
    } else {
      validData.push(item);
    }
  });

  return { validData, invalidData };
};

// 主组件
const DataImportDialog = ({
  open,
  onClose,
  onImportCompleted,
  organizations,
  chartOfAccounts,
  accountingPeriods,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [parsedData, setParsedData] = useState([]);
  const [validationResult, setValidationResult] = useState({ validData: [], invalidData: [] });
  const [importProgress, setImportProgress] = useState(0);
  const [importResult, setImportResult] = useState(null);
  const fileInputRef = useRef(null);

  // 重置状态
  const resetState = () => {
    setActiveStep(0);
    setLoading(false);
    setUploadedFile(null);
    setParsedData([]);
    setValidationResult({ validData: [], invalidData: [] });
    setImportProgress(0);
    setImportResult(null);
  };

  // 处理对话框关闭
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 处理文件上传
  const handleFileUpload = async event => {
    const file = event.target.files[0];
    if (!file) {
      return;
    }

    // 验证文件类型
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
    ];

    if (!validTypes.includes(file.type)) {
      alert('请选择Excel文件（.xlsx或.xls格式）');
      return;
    }

    setUploadedFile(file);
    setLoading(true);

    try {
      // 解析Excel文件
      const data = await parseExcelFile(file);
      setParsedData(data);
      setActiveStep(1);
    } catch (error) {
      alert('文件解析失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 处理数据验证
  const handleValidation = async () => {
    setLoading(true);

    // 模拟验证延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    const result = validateImportData(parsedData, organizations, chartOfAccounts);
    setValidationResult(result);
    setActiveStep(2);
    setLoading(false);
  };

  // 处理数据导入
  const handleImport = async () => {
    setLoading(true);
    setActiveStep(3);

    try {
      // 模拟导入进度
      for (let i = 0; i <= 100; i += 10) {
        setImportProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 调用API执行导入
      const result = await lucaNetMockApi.batchImportFinancialData(validationResult.validData);
      setImportResult(result);

      // 通知父组件导入完成
      onImportCompleted(result);
    } catch (error) {
      alert('导入失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 下载模板
  const handleDownloadTemplate = () => {
    // 在实际应用中，这里会生成并下载Excel模板
    alert('模板下载功能将生成包含所有必需列的Excel模板文件');
  };

  // 移除文件
  const handleRemoveFile = () => {
    setUploadedFile(null);
    setParsedData([]);
    setActiveStep(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{ sx: { height: '80vh' } }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">财务数据批量导入</Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<DownloadIcon />}
            onClick={handleDownloadTemplate}
          >
            下载模板
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {/* 步骤指示器 */}
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {STEPS.map(label => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* 步骤内容 */}
        {activeStep === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              选择Excel文件
            </Typography>

            {!uploadedFile ? (
              <Box
                sx={{
                  border: '2px dashed #ccc',
                  borderRadius: 2,
                  p: 4,
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': { borderColor: 'primary.main' },
                }}
                onClick={() => fileInputRef.current?.click()}
              >
                <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  点击上传Excel文件
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  支持.xlsx和.xls格式，文件大小不超过10MB
                </Typography>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                />
              </Box>
            ) : (
              <Paper sx={{ p: 2 }}>
                <Box
                  sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Box>
                    <Typography variant="subtitle1">{uploadedFile.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </Typography>
                  </Box>
                  <IconButton onClick={handleRemoveFile}>
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </Paper>
            )}

            {loading && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress />
                <Typography variant="body2" sx={{ mt: 1 }}>
                  正在解析文件...
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {activeStep === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              数据预览 ({parsedData.length} 条记录)
            </Typography>

            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>行号</TableCell>
                    <TableCell>组织</TableCell>
                    <TableCell>科目代码</TableCell>
                    <TableCell>科目名称</TableCell>
                    <TableCell>期间</TableCell>
                    <TableCell align="right">本币金额</TableCell>
                    <TableCell>币种</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {parsedData.slice(0, 10).map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{row.row}</TableCell>
                      <TableCell>{row.organizationName}</TableCell>
                      <TableCell>{row.accountCode}</TableCell>
                      <TableCell>{row.accountName}</TableCell>
                      <TableCell>{row.period}</TableCell>
                      <TableCell align="right">
                        {typeof row.localAmount === 'number'
                          ? row.localAmount.toLocaleString()
                          : row.localAmount}
                      </TableCell>
                      <TableCell>{row.localCurrency}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {parsedData.length > 10 && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                显示前10条记录，共{parsedData.length}条
              </Typography>
            )}
          </Box>
        )}

        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              验证结果
            </Typography>

            <Grid container spacing={3}>
              {/* 验证统计 */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <Chip
                    icon={<SuccessIcon />}
                    label={`有效数据: ${validationResult.validData.length}`}
                    color="success"
                    variant="outlined"
                  />
                  <Chip
                    icon={<ErrorIcon />}
                    label={`无效数据: ${validationResult.invalidData.length}`}
                    color="error"
                    variant="outlined"
                  />
                </Box>
              </Grid>

              {/* 错误列表 */}
              {validationResult.invalidData.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    数据验证错误
                  </Typography>
                  <Paper sx={{ maxHeight: 300, overflow: 'auto' }}>
                    <List dense>
                      {validationResult.invalidData.map((item, index) => (
                        <React.Fragment key={index}>
                          <ListItem>
                            <ListItemIcon>
                              <ErrorIcon color="error" />
                            </ListItemIcon>
                            <ListItemText
                              primary={`第${item.row}行: ${item.organizationName} - ${item.accountName}`}
                              secondary={item.errors?.join(', ')}
                            />
                          </ListItem>
                          {index < validationResult.invalidData.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  </Paper>
                </Grid>
              )}
            </Grid>

            {loading && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress />
                <Typography variant="body2" sx={{ mt: 1 }}>
                  正在验证数据...
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {activeStep === 3 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              导入进度
            </Typography>

            <Box sx={{ mb: 2 }}>
              <LinearProgress variant="determinate" value={importProgress} />
              <Typography variant="body2" sx={{ mt: 1 }}>
                {importProgress}% 完成
              </Typography>
            </Box>

            {importResult && (
              <Alert severity={importResult.errorCount > 0 ? 'warning' : 'success'}>
                导入完成！成功导入 {importResult.successCount} 条记录
                {importResult.errorCount > 0 && `，失败 ${importResult.errorCount} 条`}
              </Alert>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>{importResult ? '关闭' : '取消'}</Button>

        {activeStep === 1 && (
          <Button
            variant="contained"
            onClick={handleValidation}
            disabled={loading || parsedData.length === 0}
          >
            验证数据
          </Button>
        )}

        {activeStep === 2 && (
          <Button
            variant="contained"
            onClick={handleImport}
            disabled={loading || validationResult.validData.length === 0}
          >
            开始导入 ({validationResult.validData.length} 条)
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default DataImportDialog;
