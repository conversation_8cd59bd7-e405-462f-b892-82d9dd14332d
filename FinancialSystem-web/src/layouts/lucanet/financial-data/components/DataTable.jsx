/**
 * 财务数据表格组件
 *
 * 功能特性：
 * - 数据分页显示
 * - 多选功能
 * - 行内编辑
 * - 排序筛选
 * - 状态管理
 */

import React, { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Paper,
  Checkbox,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  Select,
  MenuItem,
  Box,
  Typography,
  Collapse,
} from '@mui/material';
import {
  Edit as EditIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';

// 数据状态配色
const getStatusColor = status => {
  switch (status) {
  case 'APPROVED':
    return 'success';
  case 'DRAFT':
    return 'warning';
  case 'REJECTED':
    return 'error';
  case 'PENDING':
    return 'info';
  default:
    return 'default';
  }
};

// 数据来源标签
const getDataSourceLabel = source => {
  switch (source) {
  case 'MANUAL':
    return '手动录入';
  case 'IMPORT':
    return '批量导入';
  case 'INTERFACE':
    return '接口导入';
  case 'SYSTEM':
    return '系统生成';
  default:
    return source;
  }
};

// 状态标签
const getStatusLabel = status => {
  switch (status) {
  case 'APPROVED':
    return '已审核';
  case 'DRAFT':
    return '草稿';
  case 'REJECTED':
    return '已拒绝';
  case 'PENDING':
    return '待审核';
  default:
    return status;
  }
};

// 表头定义
const headCells = [
  { id: 'organizationName', label: '组织机构', sortable: true },
  { id: 'accountCode', label: '科目代码', sortable: true },
  { id: 'accountName', label: '科目名称', sortable: true },
  { id: 'period', label: '会计期间', sortable: true },
  { id: 'localAmount', label: '本币金额', sortable: true, align: 'right' },
  { id: 'functionalAmount', label: '功能币金额', sortable: true, align: 'right' },
  { id: 'groupAmount', label: '集团币金额', sortable: true, align: 'right' },
  { id: 'localCurrency', label: '币种', sortable: true },
  { id: 'dataSource', label: '数据来源', sortable: false },
  { id: 'status', label: '状态', sortable: true },
  { id: 'createdAt', label: '创建时间', sortable: true },
  { id: 'actions', label: '操作', sortable: false },
];

// 排序功能
const getComparator = (order, orderBy) => {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
};

const descendingComparator = (a, b, orderBy) => {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
};

// 表格行组件
const DataTableRow = ({
  row,
  isSelected,
  onSelectClick,
  onEditClick,
  organizations,
  chartOfAccounts,
  editMode,
  onFieldChange,
  onSaveEdit,
  onCancelEdit,
}) => {
  const [expanded, setExpanded] = useState(false);

  // 获取组织名称
  const getOrganizationName = orgId => {
    const findOrgInTree = nodes => {
      for (const node of nodes) {
        if (node.id === orgId) {
          return node.name;
        }
        if (node.children) {
          const found = findOrgInTree(node.children);
          if (found) {
            return found;
          }
        }
      }
      return '未知组织';
    };
    return findOrgInTree(organizations);
  };

  // 格式化金额
  const formatAmount = amount => {
    if (amount === null || amount === undefined) {
      return '-';
    }
    return parseFloat(amount).toLocaleString('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    });
  };

  // 格式化日期
  const formatDate = dateString => {
    if (!dateString) {
      return '-';
    }
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <>
      <TableRow
        hover
        role="checkbox"
        aria-checked={isSelected}
        selected={isSelected}
        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
      >
        {/* 选择框 */}
        <TableCell padding="checkbox">
          <Checkbox
            color="primary"
            checked={isSelected}
            onChange={event => onSelectClick(event, row.id)}
          />
        </TableCell>

        {/* 组织机构 */}
        <TableCell>
          <Typography variant="body2">{getOrganizationName(row.organizationId)}</Typography>
        </TableCell>

        {/* 科目代码 */}
        <TableCell>
          {editMode ? (
            <TextField
              size="small"
              value={row.accountCode}
              onChange={e => onFieldChange('accountCode', e.target.value)}
              sx={{ width: 100 }}
            />
          ) : (
            <Typography variant="body2" fontFamily="monospace">
              {row.accountCode}
            </Typography>
          )}
        </TableCell>

        {/* 科目名称 */}
        <TableCell>
          <Typography variant="body2">{row.accountName}</Typography>
        </TableCell>

        {/* 会计期间 */}
        <TableCell>
          <Typography variant="body2">{row.period}</Typography>
        </TableCell>

        {/* 本币金额 */}
        <TableCell align="right">
          {editMode ? (
            <TextField
              size="small"
              type="number"
              value={row.localAmount}
              onChange={e => onFieldChange('localAmount', e.target.value)}
              sx={{ width: 120 }}
            />
          ) : (
            <Typography variant="body2" fontFamily="monospace">
              {formatAmount(row.localAmount)}
            </Typography>
          )}
        </TableCell>

        {/* 功能币金额 */}
        <TableCell align="right">
          <Typography variant="body2" fontFamily="monospace">
            {formatAmount(row.functionalAmount)}
          </Typography>
        </TableCell>

        {/* 集团币金额 */}
        <TableCell align="right">
          <Typography variant="body2" fontFamily="monospace">
            {formatAmount(row.groupAmount)}
          </Typography>
        </TableCell>

        {/* 币种 */}
        <TableCell>
          {editMode ? (
            <Select
              size="small"
              value={row.localCurrency}
              onChange={e => onFieldChange('localCurrency', e.target.value)}
              sx={{ width: 80 }}
            >
              <MenuItem value="CNY">CNY</MenuItem>
              <MenuItem value="USD">USD</MenuItem>
              <MenuItem value="HKD">HKD</MenuItem>
              <MenuItem value="EUR">EUR</MenuItem>
            </Select>
          ) : (
            <Typography variant="body2">{row.localCurrency}</Typography>
          )}
        </TableCell>

        {/* 数据来源 */}
        <TableCell>
          <Chip label={getDataSourceLabel(row.dataSource)} size="small" variant="outlined" />
        </TableCell>

        {/* 状态 */}
        <TableCell>
          <Chip
            label={getStatusLabel(row.status)}
            size="small"
            color={getStatusColor(row.status)}
          />
        </TableCell>

        {/* 创建时间 */}
        <TableCell>
          <Typography variant="body2">{formatDate(row.createdAt)}</Typography>
        </TableCell>

        {/* 操作 */}
        <TableCell>
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {editMode ? (
              <>
                <Tooltip title="保存">
                  <IconButton size="small" onClick={onSaveEdit}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="取消">
                  <IconButton size="small" onClick={onCancelEdit}>
                    <ExpandLessIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </>
            ) : (
              <>
                <Tooltip title="编辑">
                  <IconButton size="small" onClick={() => onEditClick(row)}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="查看详情">
                  <IconButton size="small" onClick={() => setExpanded(!expanded)}>
                    {expanded ? (
                      <ExpandLessIcon fontSize="small" />
                    ) : (
                      <ExpandMoreIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </>
            )}
          </Box>
        </TableCell>
      </TableRow>

      {/* 展开行 - 显示详细信息 */}
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={headCells.length + 1}>
          <Collapse in={expanded} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1, p: 2, bgcolor: 'grey.50' }}>
              <Typography variant="subtitle2" gutterBottom>
                详细信息
              </Typography>
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: 2,
                }}
              >
                <Typography variant="body2">
                  <strong>数据ID:</strong> {row.id}
                </Typography>
                <Typography variant="body2">
                  <strong>功能币币种:</strong> {row.functionalCurrency}
                </Typography>
                <Typography variant="body2">
                  <strong>集团币币种:</strong> {row.groupCurrency}
                </Typography>
                <Typography variant="body2">
                  <strong>业务场景:</strong> {row.scenarioCode || 'ACTUAL'}
                </Typography>
                {row.notes && (
                  <Typography variant="body2">
                    <strong>备注:</strong> {row.notes}
                  </Typography>
                )}
                <Typography variant="body2">
                  <strong>最后更新:</strong> {formatDate(row.updatedAt || row.createdAt)}
                </Typography>
              </Box>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
};

// 主表格组件
const DataTable = ({
  data,
  organizations,
  chartOfAccounts,
  selectedRows,
  onSelectionChange,
  onRowEdit,
}) => {
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('createdAt');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [editingRow, setEditingRow] = useState(null);
  const [editingData, setEditingData] = useState({});

  // 排序处理
  const handleRequestSort = property => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // 全选处理
  const handleSelectAllClick = event => {
    if (event.target.checked) {
      const newSelected = visibleRows.map(n => n.id);
      onSelectionChange(newSelected);
    } else {
      onSelectionChange([]);
    }
  };

  // 单行选择处理
  const handleSelectClick = (event, id) => {
    const selectedIndex = selectedRows.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedRows, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedRows.slice(1));
    } else if (selectedIndex === selectedRows.length - 1) {
      newSelected = newSelected.concat(selectedRows.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selectedRows.slice(0, selectedIndex),
        selectedRows.slice(selectedIndex + 1),
      );
    }

    onSelectionChange(newSelected);
  };

  // 分页处理
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = event => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 编辑处理
  const handleEditClick = row => {
    setEditingRow(row.id);
    setEditingData({ ...row });
  };

  const handleFieldChange = (field, value) => {
    setEditingData(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveEdit = () => {
    // 调用父组件的编辑保存方法
    onRowEdit(editingData);
    setEditingRow(null);
    setEditingData({});
  };

  const handleCancelEdit = () => {
    setEditingRow(null);
    setEditingData({});
  };

  // 计算可见行
  const visibleRows = useMemo(() => {
    return data
      .slice()
      .sort(getComparator(order, orderBy))
      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  }, [data, order, orderBy, page, rowsPerPage]);

  const isSelected = id => selectedRows.indexOf(id) !== -1;
  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - data.length) : 0;

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ width: '100%', mb: 2 }}>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                {/* 全选框 */}
                <TableCell padding="checkbox">
                  <Checkbox
                    color="primary"
                    indeterminate={selectedRows.length > 0 && selectedRows.length < data.length}
                    checked={data.length > 0 && selectedRows.length === data.length}
                    onChange={handleSelectAllClick}
                  />
                </TableCell>

                {/* 表头 */}
                {headCells.map(headCell => (
                  <TableCell
                    key={headCell.id}
                    align={headCell.align || 'left'}
                    sortDirection={orderBy === headCell.id ? order : false}
                  >
                    {headCell.sortable ? (
                      <TableSortLabel
                        active={orderBy === headCell.id}
                        direction={orderBy === headCell.id ? order : 'asc'}
                        onClick={() => handleRequestSort(headCell.id)}
                      >
                        {headCell.label}
                      </TableSortLabel>
                    ) : (
                      headCell.label
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {visibleRows.map(row => (
                <DataTableRow
                  key={row.id}
                  row={editingRow === row.id ? editingData : row}
                  isSelected={isSelected(row.id)}
                  onSelectClick={handleSelectClick}
                  onEditClick={handleEditClick}
                  organizations={organizations}
                  chartOfAccounts={chartOfAccounts}
                  editMode={editingRow === row.id}
                  onFieldChange={handleFieldChange}
                  onSaveEdit={handleSaveEdit}
                  onCancelEdit={handleCancelEdit}
                />
              ))}

              {emptyRows > 0 && (
                <TableRow style={{ height: 53 * emptyRows }}>
                  <TableCell colSpan={headCells.length + 1} />
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={data.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`} 条`
          }
        />
      </Paper>
    </Box>
  );
};

export default DataTable;
